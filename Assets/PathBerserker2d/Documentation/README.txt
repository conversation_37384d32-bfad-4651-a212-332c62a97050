Pathberserker2d

Thanks for purchasing. To get started, I suggest trying out the various demo scenes.

The documentation is shipped as a zip file in the same folder.
Alternatively you can read the most up to date version of the documentation online here:
https://oribow.github.io/PathBerserker2dDemo/Documentation/

The documentation also includes guides on a variety of topics and explains the core concepts of this asset.

As an extra treat, this Asset also comes with its complete source code, also as zip in this folder. 
Setting your project up to use the source code instead of the DLL's requires some knowledge about C# projects.
I recommend using the source code primarily as a reference guide.

Happy path-finding!