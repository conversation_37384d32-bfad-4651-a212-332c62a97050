using UnityEngine;
using PathBerserker2d;

namespace BotSystem
{
    /// <summary>
    /// Follower с NavTag системой избегания
    /// Использует AvoidanceMarker для создания зон избегания и NavAgent для следования
    /// Все настройки централизованы в SimpleBotManager
    /// </summary>
    [RequireComponent(typeof(NavAgent))]
    public class AvoidanceFollower : MonoBehaviour
    {
        [Header("Target")]
        public Transform target;

        [Header("Following Settings (настраиваются из BotManager)")]
        [HideInInspector] public float closeEnoughRadius = 3.0f;
        [HideInInspector] public float travelStopRadius = 1.0f;
        [HideInInspector] public float targetPredictionTime = 0.1f;
        [HideInInspector] public float pathUpdateInterval = 0.3f;

        // Компоненты
        private NavAgent navAgent;
        private AvoidanceMarker avoidanceMarker;
        private SimpleBotManager botManager;

        // Состояние следования
        private Vector2 lastTargetPosition;
        private float lastPathUpdateTime;
        private bool isInitialized;

        private void Awake()
        {
            navAgent = GetComponent<NavAgent>();
            if (navAgent == null)
            {
                Debug.LogError($"AvoidanceFollower: NavAgent не найден на {gameObject.name}!");
                enabled = false;
                return;
            }
        }

        private void Start()
        {
            InitializeComponents();
        }

        private void OnDestroy()
        {
            // Отписываемся от событий NavAgent
            if (navAgent != null)
            {
                navAgent.OnFailedToFindPath -= OnFailedToFindPath;
            }
        }

        /// <summary>
        /// Инициализация компонентов
        /// </summary>
        private void InitializeComponents()
        {
            botManager = SimpleBotManager.Instance;
            if (botManager == null)
            {
                Debug.LogError($"AvoidanceFollower: SimpleBotManager не найден для {gameObject.name}!");
                return;
            }

            // Получаем или создаем AvoidanceMarker
            avoidanceMarker = GetComponent<AvoidanceMarker>();
            if (avoidanceMarker == null)
            {
                avoidanceMarker = gameObject.AddComponent<AvoidanceMarker>();
            }

            // Подписываемся на события NavAgent
            navAgent.OnFailedToFindPath += OnFailedToFindPath;

            isInitialized = true;
        }

        private void Update()
        {
            if (!isInitialized || target == null || navAgent == null)
            {
                if (!isInitialized) Debug.LogWarning($"AvoidanceFollower {gameObject.name}: Не инициализирован");
                if (target == null) Debug.LogWarning($"AvoidanceFollower {gameObject.name}: Нет цели");
                if (navAgent == null) Debug.LogWarning($"AvoidanceFollower {gameObject.name}: Нет NavAgent");
                return;
            }

            // Обновляем следование к цели с заданным интервалом
            if (Time.time - lastPathUpdateTime >= pathUpdateInterval)
            {
                UpdateTargetFollowing();
                lastPathUpdateTime = Time.time;
            }
        }



        /// <summary>
        /// Обновление следования к цели с NavTag избеганием
        /// </summary>
        private void UpdateTargetFollowing()
        {
            if (target == null)
                return;

            Vector2 targetPos = GetTargetPosition();
            float distToTarget = Vector2.Distance(navAgent.Position, targetPos);

            // Проверяем, нужно ли обновить путь
            if (distToTarget > closeEnoughRadius)
            {
                // Проверяем, изменилась ли позиция цели значительно или агент не следует пути
                if (Vector2.Distance(lastTargetPosition, targetPos) > 1.0f || !navAgent.IsFollowingAPath)
                {
                    // NavAgent автоматически учтет NavTag стоимости при построении пути
                    navAgent.UpdatePath(targetPos);
                    lastTargetPosition = targetPos;
                }
            }
            else if (distToTarget < travelStopRadius)
            {
                navAgent.Stop();
            }
        }

        /// <summary>
        /// Получение позиции цели с предсказанием
        /// </summary>
        private Vector2 GetTargetPosition()
        {
            if (targetPredictionTime <= 0 || target == null)
                return target.position;

            // Простое предсказание на основе скорости цели
            Rigidbody2D targetRb = target.GetComponent<Rigidbody2D>();
            if (targetRb != null)
            {
                Vector2 predictedPos = (Vector2)target.position + targetRb.velocity * targetPredictionTime;
                return predictedPos;
            }

            return target.position;
        }

        /// <summary>
        /// Обработка неудачи поиска пути
        /// </summary>
        private void OnFailedToFindPath(NavAgent agent)
        {
            // Пытаемся повторить поиск пути через некоторое время
            if (target != null)
            {
                Invoke(nameof(RetryPathfinding), 1.0f);
            }
        }

        /// <summary>
        /// Повторная попытка поиска пути
        /// </summary>
        private void RetryPathfinding()
        {
            if (target != null && navAgent != null)
            {
                navAgent.PathTo(target.position);
            }
        }

        /// <summary>
        /// Настройка параметров следования (вызывается из SimpleBotManager)
        /// </summary>
        public void ConfigureFollowing(float closeEnough, float stopRadius, float predictionTime, float updateInterval)
        {
            closeEnoughRadius = closeEnough;
            travelStopRadius = stopRadius;
            targetPredictionTime = predictionTime;
            pathUpdateInterval = updateInterval;
        }

        /// <summary>
        /// Получение AvoidanceMarker компонента
        /// </summary>
        public AvoidanceMarker GetAvoidanceMarker()
        {
            return avoidanceMarker;
        }

        /// <summary>
        /// Принудительное обновление пути к цели
        /// </summary>
        public void ForceUpdatePath()
        {
            if (target != null && navAgent != null)
            {
                navAgent.UpdatePath(GetTargetPosition());
                lastTargetPosition = GetTargetPosition();
                lastPathUpdateTime = Time.time;
            }
        }


    }
}
