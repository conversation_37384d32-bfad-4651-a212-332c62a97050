using UnityEngine;
using PathBerserker2d;

namespace BotSystem
{
    /// <summary>
    /// Follower с NavTag системой избегания
    /// Использует AvoidanceMarker для создания зон избегания и NavAgent для следования
    /// Все настройки централизованы в SimpleBotManager
    /// </summary>
    [RequireComponent(typeof(NavAgent))]
    public class AvoidanceFollower : MonoBehaviour
    {
        [Header("Target")]
        public Transform target;

        [Header("Following Settings (настраиваются из BotManager)")]
        [HideInInspector] public float closeEnoughRadius = 3.0f;
        [HideInInspector] public float travelStopRadius = 1.0f;
        [HideInInspector] public float targetPredictionTime = 0.1f;
        [HideInInspector] public float pathUpdateInterval = 0.3f;

        // NavTag константы
        private const int ENEMY_TAG = 1;
        private const int ALLY_TAG = 2;

        // Компоненты
        private NavAgent navAgent;
        private NavAreaMarker avoidanceMarker;
        private SimpleBotManager botManager;

        // Состояние следования
        private Vector2 lastTargetPosition;
        private float lastPathUpdateTime;
        private bool isInitialized;

        // Состояние избегания
        private int myTeamNumber;
        private Vector3 lastMarkerPosition;
        private float lastMarkerUpdateTime;
        private float avoidanceRadius = 2f;
        private float avoidanceHeight = 1f;
        private float markerUpdateThreshold = 0.1f;
        private float markerUpdateInterval = 0.1f;

        private void Awake()
        {
            navAgent = GetComponent<NavAgent>();
            if (navAgent == null)
            {
                Debug.LogError($"AvoidanceFollower: NavAgent не найден на {gameObject.name}!");
                enabled = false;
                return;
            }
        }

        private void Start()
        {
            InitializeComponents();
        }

        private void OnDestroy()
        {
            // Отписываемся от событий NavAgent
            if (navAgent != null)
            {
                navAgent.OnFailedToFindPath -= OnFailedToFindPath;
            }

            // Очищаем маркер избегания
            if (avoidanceMarker != null)
            {
                DestroyImmediate(avoidanceMarker.gameObject);
            }
        }

        /// <summary>
        /// Инициализация компонентов
        /// </summary>
        private void InitializeComponents()
        {
            botManager = SimpleBotManager.Instance;
            if (botManager == null)
            {
                Debug.LogError($"AvoidanceFollower: SimpleBotManager не найден для {gameObject.name}!");
                return;
            }

            // Определяем команду бота
            myTeamNumber = botManager.GetBotTeamNumber(gameObject);
            if (myTeamNumber == 0)
            {
                Debug.LogError($"AvoidanceFollower: Бот {gameObject.name} не принадлежит ни к одной команде!");
                return;
            }

            // Создаем маркер избегания
            CreateAvoidanceMarker();

            // Подписываемся на события NavAgent
            navAgent.OnFailedToFindPath += OnFailedToFindPath;

            isInitialized = true;
        }

        private void Update()
        {
            if (!isInitialized || target == null || navAgent == null)
            {
                if (!isInitialized) Debug.LogWarning($"AvoidanceFollower {gameObject.name}: Не инициализирован");
                if (target == null) Debug.LogWarning($"AvoidanceFollower {gameObject.name}: Нет цели");
                if (navAgent == null) Debug.LogWarning($"AvoidanceFollower {gameObject.name}: Нет NavAgent");
                return;
            }

            // Обновляем маркер избегания при движении
            UpdateAvoidanceMarker();

            // Обновляем следование к цели с заданным интервалом
            if (Time.time - lastPathUpdateTime >= pathUpdateInterval)
            {
                UpdateTargetFollowing();
                lastPathUpdateTime = Time.time;
            }
        }



        /// <summary>
        /// Обновление следования к цели с NavTag избеганием
        /// </summary>
        private void UpdateTargetFollowing()
        {
            if (target == null)
                return;

            Vector2 targetPos = GetTargetPosition();
            float distToTarget = Vector2.Distance(navAgent.Position, targetPos);

            // Проверяем, нужно ли обновить путь
            if (distToTarget > closeEnoughRadius)
            {
                // Проверяем, изменилась ли позиция цели значительно или агент не следует пути
                if (Vector2.Distance(lastTargetPosition, targetPos) > 1.0f || !navAgent.IsFollowingAPath)
                {
                    // NavAgent автоматически учтет NavTag стоимости при построении пути
                    navAgent.UpdatePath(targetPos);
                    lastTargetPosition = targetPos;
                }
            }
            else if (distToTarget < travelStopRadius)
            {
                navAgent.Stop();
            }
        }

        /// <summary>
        /// Получение позиции цели с предсказанием
        /// </summary>
        private Vector2 GetTargetPosition()
        {
            if (targetPredictionTime <= 0 || target == null)
                return target.position;

            // Простое предсказание на основе скорости цели
            Rigidbody2D targetRb = target.GetComponent<Rigidbody2D>();
            if (targetRb != null)
            {
                Vector2 predictedPos = (Vector2)target.position + targetRb.velocity * targetPredictionTime;
                return predictedPos;
            }

            return target.position;
        }

        /// <summary>
        /// Обработка неудачи поиска пути
        /// </summary>
        private void OnFailedToFindPath(NavAgent agent)
        {
            // Пытаемся повторить поиск пути через некоторое время
            if (target != null)
            {
                Invoke(nameof(RetryPathfinding), 1.0f);
            }
        }

        /// <summary>
        /// Повторная попытка поиска пути
        /// </summary>
        private void RetryPathfinding()
        {
            if (target != null && navAgent != null)
            {
                navAgent.PathTo(target.position);
            }
        }

        /// <summary>
        /// Создание маркера избегания
        /// </summary>
        private void CreateAvoidanceMarker()
        {
            // Создаем дочерний объект для маркера
            GameObject markerObj = new GameObject($"AvoidanceMarker_Team{myTeamNumber}");
            markerObj.transform.SetParent(transform, false);
            markerObj.transform.localPosition = Vector3.zero;

            // Добавляем NavAreaMarker
            avoidanceMarker = markerObj.AddComponent<NavAreaMarker>();

            // Настраиваем NavTag в зависимости от команды
            // Для других команд этот бот будет врагом (ENEMY_TAG)
            // Для своей команды - союзником (ALLY_TAG)
            avoidanceMarker.NavTag = ENEMY_TAG; // По умолчанию враг для всех

            // Настраиваем размер зоны
            RectTransform rect = markerObj.GetComponent<RectTransform>();
            rect.sizeDelta = new Vector2(avoidanceRadius * 2, avoidanceHeight * 2);
            rect.localRotation = Quaternion.identity;

            // Настраиваем параметры обновления
            avoidanceMarker.updateAfterTimeOfNoMovement = markerUpdateInterval;

            lastMarkerPosition = transform.position;
            lastMarkerUpdateTime = Time.time;
        }

        /// <summary>
        /// Обновление маркера избегания при движении
        /// </summary>
        private void UpdateAvoidanceMarker()
        {
            if (avoidanceMarker == null)
                return;

            // Проверяем, нужно ли обновить маркер
            float timeSinceUpdate = Time.time - lastMarkerUpdateTime;
            float distanceMoved = Vector3.Distance(transform.position, lastMarkerPosition);

            if (distanceMoved > markerUpdateThreshold && timeSinceUpdate >= markerUpdateInterval)
            {
                lastMarkerPosition = transform.position;
                lastMarkerUpdateTime = Time.time;

                // NavAreaMarker автоматически обновится благодаря своей внутренней логике
                avoidanceMarker.transform.hasChanged = true;
            }
        }

        /// <summary>
        /// Настройка параметров следования (вызывается из SimpleBotManager)
        /// </summary>
        public void ConfigureFollowing(float closeEnough, float stopRadius, float predictionTime, float updateInterval)
        {
            closeEnoughRadius = closeEnough;
            travelStopRadius = stopRadius;
            targetPredictionTime = predictionTime;
            pathUpdateInterval = updateInterval;
        }

        /// <summary>
        /// Настройка параметров избегания (вызывается из SimpleBotManager)
        /// </summary>
        public void ConfigureAvoidance(float radius, float height, float threshold, float interval)
        {
            avoidanceRadius = radius;
            avoidanceHeight = height;
            markerUpdateThreshold = threshold;
            markerUpdateInterval = interval;

            // Обновляем размер маркера, если он уже создан
            if (avoidanceMarker != null)
            {
                RectTransform rect = avoidanceMarker.GetComponent<RectTransform>();
                rect.sizeDelta = new Vector2(avoidanceRadius * 2, avoidanceHeight * 2);
                avoidanceMarker.updateAfterTimeOfNoMovement = markerUpdateInterval;
            }
        }

        /// <summary>
        /// Получение NavAreaMarker компонента
        /// </summary>
        public NavAreaMarker GetAvoidanceMarker()
        {
            return avoidanceMarker;
        }

        /// <summary>
        /// Получение текущего NavTag маркера
        /// </summary>
        public int GetNavTag()
        {
            return avoidanceMarker != null ? avoidanceMarker.NavTag : 0;
        }

        /// <summary>
        /// Включение/выключение маркера
        /// </summary>
        public void SetMarkerEnabled(bool enabled)
        {
            if (avoidanceMarker != null)
            {
                avoidanceMarker.gameObject.SetActive(enabled);
            }
        }

        /// <summary>
        /// Получение радиуса избегания
        /// </summary>
        public float GetAvoidanceRadius()
        {
            return avoidanceRadius;
        }

        /// <summary>
        /// Установка NavTag для маркера (для динамического изменения поведения)
        /// </summary>
        public void SetNavTag(int navTag)
        {
            if (avoidanceMarker != null)
            {
                avoidanceMarker.NavTag = navTag;
            }
        }

        /// <summary>
        /// Принудительное обновление пути к цели
        /// </summary>
        public void ForceUpdatePath()
        {
            if (target != null && navAgent != null)
            {
                navAgent.UpdatePath(GetTargetPosition());
                lastTargetPosition = GetTargetPosition();
                lastPathUpdateTime = Time.time;
            }
        }


    }
}
